import { indexedDBService } from '../services/indexedDBService';
import type { Parameter, ShorelineSegment, ParameterValue, ShorelineSegmentProperties } from '../types';
import * as turf from '@turf/turf';

export const applyParameterValueToSegments = async (
    segments: ShorelineSegment[],
    selectedSegmentIds: string[],
    activeParameter: Parameter,
    valueToApply: string,
    vulnerabilityToApply: number
): Promise<ShorelineSegment[]> => {
    console.log(`Applying value "${valueToApply}" (vuln: ${vulnerabilityToApply}) for "${activeParameter.name}" to ${selectedSegmentIds.length} segments`);

    // Create a Set for O(1) lookup performance
    const selectedIdsSet = new Set(selectedSegmentIds);

    // Prepare parameter value
    let paramValue: ParameterValue;
    if (activeParameter.type === 'numerical') {
        const numValue = parseFloat(valueToApply);
        if (isNaN(numValue)) {
            throw new Error(`Invalid numerical value: ${valueToApply}`);
        }
        paramValue = { type: 'numerical', value: numValue, vulnerability: vulnerabilityToApply };
    } else {
        paramValue = { type: 'categorical', value: valueToApply, vulnerability: vulnerabilityToApply };
    }

    // Optimized update: only process segments that need updating
    let updateOccurred = false;
    const updatedSegments = segments.map(segment => {
        if (!selectedIdsSet.has(segment.id)) {
            return segment; // No change needed
        }

        const existingValue = segment.parameters?.[activeParameter.id];
        const needsUpdate = !existingValue ||
                           existingValue.value !== paramValue.value ||
                           existingValue.vulnerability !== paramValue.vulnerability;

        if (!needsUpdate) {
            return segment; // No change needed
        }

        updateOccurred = true;

        // Update both direct parameters and properties.parameters
        const updatedDirectParameters = { ...segment.parameters, [activeParameter.id]: paramValue };
        const currentProperties = segment.properties || { id: segment.id } as ShorelineSegmentProperties;
        const currentPropertiesParams = currentProperties.parameters && typeof currentProperties.parameters === 'object'
                                       ? currentProperties.parameters
                                       : {};
        const updatedProperties: ShorelineSegmentProperties = {
            ...currentProperties,
            parameters: { ...currentPropertiesParams, [activeParameter.id]: paramValue }
        };

        return { ...segment, parameters: updatedDirectParameters, properties: updatedProperties };
    });

    // Always return a new array reference to ensure React detects changes
    const finalSegments = updateOccurred ? updatedSegments : [...segments];

    // Optimized storage: defer database updates for better performance
    if (updateOccurred) {
        try {
            console.log(`Updated ${selectedSegmentIds.length} segments in memory`);

            // For large datasets, defer the database write to avoid blocking the UI
            if (segments.length > 10000) {
                // Use setTimeout to defer the database write and keep UI responsive
                setTimeout(async () => {
                    try {
                        await storeSegmentsOptimized(finalSegments);
                        console.log("Background database update completed");
                    } catch (error) {
                        console.error('Background database update failed:', error);
                    }
                }, 100);
            } else {
                // For smaller datasets, update immediately
                await storeSegmentsOptimized(finalSegments);
                console.log("Database updated successfully");
            }
        } catch (error) {
            console.error('Failed to update segments:', error);
            throw new Error(`Failed to save changes: ${error instanceof Error ? error.message : String(error)}`);
        }
    } else {
        console.log("No segment values changed, skipping update.");
    }

    return finalSegments;
};

// Optimized storage function that minimizes memory allocation
export async function storeSegmentsOptimized(segments: ShorelineSegment[], processingTime?: number): Promise<void> {
    const startTime = performance.now();

    // Convert segments to features efficiently
    const features = new Array(segments.length);
    let totalLength = 0;
    for (let i = 0; i < segments.length; i++) {
        features[i] = {
            type: 'Feature' as const,
            geometry: segments[i].geometry,
            properties: segments[i].properties,
        };
        totalLength += segments[i].properties?.length || 0;
    }

    // Calculate bounds for the segments
    const bounds = calculateSegmentBounds(segments);

    // Store the segment data
    await indexedDBService.storeShorelineData('current-segments', {
        type: 'FeatureCollection',
        features
    }, bounds);

    // Store metadata for fast access
    const metadata = {
        id: 'current-segments',
        totalSegments: segments.length,
        bounds: bounds || [[-90, -180], [90, 180]] as [[number, number], [number, number]],
        timestamp: Date.now(),
        isMemoryOptimized: segments.length > 10000,
        averageSegmentLength: segments.length > 0 ? totalLength / segments.length : 0,
        processingTime: processingTime || 0
    };

    await indexedDBService.storeSegmentMetadata(metadata);

    const endTime = performance.now();
    console.log(`Database storage completed in ${(endTime - startTime).toFixed(2)}ms`);
}

// Helper function to calculate bounds from segments
function calculateSegmentBounds(segments: ShorelineSegment[]): [[number, number], [number, number]] | null {
    if (segments.length === 0) return null;

    let minLat = Infinity, minLng = Infinity;
    let maxLat = -Infinity, maxLng = -Infinity;

    for (const segment of segments) {
        try {
            const bbox = turf.bbox(segment);
            const [west, south, east, north] = bbox;

            minLng = Math.min(minLng, west);
            minLat = Math.min(minLat, south);
            maxLng = Math.max(maxLng, east);
            maxLat = Math.max(maxLat, north);
        } catch (error) {
            console.warn('Error calculating bounds for segment:', segment.id, error);
        }
    }

    if (minLat === Infinity) return null;
    return [[minLat, minLng], [maxLat, maxLng]];
}
